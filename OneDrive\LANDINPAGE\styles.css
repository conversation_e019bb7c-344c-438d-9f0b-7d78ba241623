/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
}

.logo i {
    margin-left: 10px;
    font-size: 1.8rem;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s;
}

.nav a:hover {
    opacity: 0.8;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-description {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-cta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cta-button {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    padding: 1.2rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    justify-content: center;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
}

.cta-button.large {
    padding: 1.5rem 3rem;
    font-size: 1.4rem;
}

.urgency-text {
    color: #ffeb3b;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.product-showcase {
    position: relative;
    text-align: center;
}

.product-img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.floating-badges {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.badge {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    padding: 8px 15px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    animation: float 3s ease-in-out infinite;
}

.badge:nth-child(1) {
    top: 20%;
    right: -10%;
    animation-delay: 0s;
}

.badge:nth-child(2) {
    top: 50%;
    left: -15%;
    animation-delay: 1s;
}

.badge:nth-child(3) {
    bottom: 20%;
    right: -5%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Countdown Section */
.countdown-section {
    background: #1a1a1a;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.countdown-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ff6b35;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.time-unit {
    text-align: center;
}

.time-value {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    color: #ff6b35;
}

.time-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Section Styles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #333;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #f8f9fa;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Ingredients Section */
.ingredients {
    padding: 80px 0;
    background: white;
}

.ingredients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.ingredient-card {
    text-align: center;
    padding: 2rem;
}

.ingredient-image {
    width: 150px;
    height: 150px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.ingredient-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ingredient-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ff6b35;
}

.ingredient-card p {
    color: #666;
    line-height: 1.6;
}

/* Benefits Section */
.benefits {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.benefits .section-title {
    color: white;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.benefit-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.benefit-card p {
    opacity: 0.9;
    line-height: 1.6;
}

/* Before/After Section */
.before-after {
    padding: 80px 0;
    background: #f8f9fa;
}

.comparison {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
}

.before, .after {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.before h3 {
    color: #e74c3c;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.after h3 {
    color: #27ae60;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.before ul, .after ul {
    list-style: none;
}

.before li, .after li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.before li:last-child, .after li:last-child {
    border-bottom: none;
}

.arrow {
    font-size: 2rem;
    color: #ff6b35;
    font-weight: bold;
}

/* How to Use Section */
.how-to-use {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.how-to-use .section-title {
    color: white;
}

.steps-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.step-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s;
    min-width: 250px;
}

.step-card:hover {
    transform: translateY(-10px);
}

.step-number {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: #ff6b35;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.step-content h3 {
    margin: 1rem 0;
    font-size: 1.3rem;
}

.step-image {
    font-size: 3rem;
    margin: 1rem 0;
}

.step-arrow {
    font-size: 2rem;
    color: #ffeb3b;
    animation: bounce 2s infinite;
}

/* Enhanced Testimonials Section */
.testimonials-enhanced {
    padding: 80px 0;
    background: #f8f9fa;
}

.testimonials-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card-enhanced {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 2rem;
    border-radius: 20px;
    display: flex;
    gap: 1.5rem;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
    transition: transform 0.3s;
}

.testimonial-card-enhanced:hover {
    transform: translateY(-5px);
}

.customer-photo {
    flex-shrink: 0;
}

.customer-photo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid white;
}

.testimonial-content {
    flex: 1;
}

.stars-large {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.testimonial-content h4 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.testimonial-content p {
    line-height: 1.6;
    margin-bottom: 1rem;
    font-style: italic;
}

.verified-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    display: inline-block;
}

/* Medical Endorsement Section */
.medical-endorsement {
    padding: 80px 0;
    background: white;
}

.doctors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.doctor-card {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: transform 0.3s;
}

.doctor-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.doctor-card img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin-bottom: 1rem;
    border: 4px solid #ff6b35;
}

.doctor-card h4 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.doctor-card p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.doctor-quote {
    background: #ff6b35;
    color: white;
    padding: 1rem;
    border-radius: 10px;
    font-style: italic;
    position: relative;
}

.doctor-quote::before {
    content: '"';
    font-size: 2rem;
    position: absolute;
    top: -10px;
    left: 10px;
    color: rgba(255, 255, 255, 0.5);
}

/* Special Offer Section */
.special-offer {
    padding: 80px 0;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
}

.special-offer .section-title {
    color: white;
}

.offer-packages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.package {
    background: white;
    color: #333;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s;
}

.package:hover {
    transform: translateY(-5px);
}

.package.popular {
    transform: scale(1.05);
    border: 3px solid #ffeb3b;
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #ffeb3b;
    color: #333;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
}

.package-image {
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
    border-radius: 10px;
    overflow: hidden;
}

.package-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.package h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
}

.price {
    margin-bottom: 1rem;
}

.old-price {
    text-decoration: line-through;
    color: #999;
    font-size: 1rem;
    display: block;
}

.new-price {
    color: #ff6b35;
    font-size: 2rem;
    font-weight: 700;
}

.savings {
    background: #27ae60;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: inline-block;
}

.package-btn {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.package-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
    color: white;
}

/* FAQ Section */
.faq {
    padding: 80px 0;
    background: #f8f9fa;
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    margin-bottom: 1rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.faq-question {
    padding: 1.5rem;
    background: #ff6b35;
    color: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
}

.faq-question:hover {
    background: #e55a2b;
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s;
}

.faq-answer.active {
    padding: 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    line-height: 1.6;
    color: #555;
}

/* Final CTA Section */
.final-cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #1a1a1a, #333);
    color: white;
    text-align: center;
}

.final-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: #ff6b35;
}

.cta-text {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.cta-subtext {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.final-cta-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.guarantee {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #27ae60;
    font-weight: 600;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3, .footer-section h4 {
    margin-bottom: 1rem;
    color: #ff6b35;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: #ff6b35;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #333;
    color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .nav {
        display: none;
    }

    .countdown-timer {
        gap: 1rem;
    }

    .time-value {
        font-size: 2rem;
    }

    .comparison {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .arrow {
        transform: rotate(90deg);
    }

    .offer-packages {
        grid-template-columns: 1fr;
    }

    .package.popular {
        transform: none;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .container {
        padding: 0 15px;
    }
}
