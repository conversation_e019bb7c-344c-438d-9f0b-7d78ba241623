<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب المنتج - Vitalénergie</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .order-form-container {
            max-width: 600px;
            margin: 120px auto 50px;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .form-header h1 {
            color: #ff6b35;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ff6b35;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .package-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .package-option {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .package-option:hover {
            border-color: #ff6b35;
        }
        
        .package-option.selected {
            border-color: #ff6b35;
            background: #fff5f0;
        }
        
        .package-option input[type="radio"] {
            display: none;
        }
        
        .package-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #ff6b35;
            margin-top: 0.5rem;
        }
        
        .order-summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .summary-total {
            border-top: 2px solid #ddd;
            padding-top: 1rem;
            font-weight: 700;
            font-size: 1.2rem;
            color: #ff6b35;
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 1.5rem;
            font-size: 1.2rem;
            font-weight: 700;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
        }
        
        .security-info {
            text-align: center;
            margin-top: 1rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #ff6b35;
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 600;
        }
        
        .back-link:hover {
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .package-selection {
                grid-template-columns: 1fr;
            }
            
            .order-form-container {
                margin: 100px 20px 50px;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-bolt"></i>
                <span>Vitalénergie</span>
            </div>
        </div>
    </header>

    <div class="order-form-container">
        <a href="index.html" class="back-link">
            <i class="fas fa-arrow-right"></i>
            العودة للصفحة الرئيسية
        </a>
        
        <div class="form-header">
            <h1>🛒 اطلب منتجك الآن</h1>
            <p>املأ النموذج أدناه وسنتواصل معك خلال 24 ساعة</p>
        </div>

        <form id="orderForm" onsubmit="submitOrder(event)">
            <!-- Package Selection -->
            <div class="form-group">
                <label>اختر العرض المناسب لك:</label>
                <div class="package-selection">
                    <div class="package-option" onclick="selectPackage(this, 'single', 199)">
                        <input type="radio" name="package" value="single" required>
                        <h3>عبوة واحدة</h3>
                        <div class="package-price">199 درهم</div>
                    </div>
                    <div class="package-option" onclick="selectPackage(this, 'double', 299)">
                        <input type="radio" name="package" value="double" required>
                        <h3>اشتري 1 + 1 مجاناً</h3>
                        <div class="package-price">299 درهم</div>
                        <small style="color: #27ae60;">وفر 299 درهم</small>
                    </div>
                    <div class="package-option" onclick="selectPackage(this, 'triple', 399)">
                        <input type="radio" name="package" value="triple" required>
                        <h3>اشتري 2 + 1 مجاناً</h3>
                        <div class="package-price">399 درهم</div>
                        <small style="color: #27ae60;">وفر 498 درهم</small>
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName">الاسم الأول *</label>
                    <input type="text" id="firstName" name="firstName" required>
                </div>
                <div class="form-group">
                    <label for="lastName">الاسم الأخير *</label>
                    <input type="text" id="lastName" name="lastName" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="phone">رقم الهاتف *</label>
                    <input type="tel" id="phone" name="phone" required placeholder="+212 6XX XXX XXX">
                </div>
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>">
                </div>
            </div>

            <!-- Address Information -->
            <div class="form-group">
                <label for="city">المدينة *</label>
                <select id="city" name="city" required>
                    <option value="">اختر المدينة</option>
                    <option value="casablanca">الدار البيضاء</option>
                    <option value="rabat">الرباط</option>
                    <option value="marrakech">مراكش</option>
                    <option value="fes">فاس</option>
                    <option value="tangier">طنجة</option>
                    <option value="agadir">أكادير</option>
                    <option value="meknes">مكناس</option>
                    <option value="oujda">وجدة</option>
                    <option value="kenitra">القنيطرة</option>
                    <option value="tetouan">تطوان</option>
                    <option value="other">أخرى</option>
                </select>
            </div>

            <div class="form-group">
                <label for="address">العنوان الكامل *</label>
                <textarea id="address" name="address" rows="3" required placeholder="الحي، الشارع، رقم المنزل..."></textarea>
            </div>

            <!-- Order Summary -->
            <div class="order-summary">
                <h3>ملخص الطلب</h3>
                <div class="summary-row">
                    <span>المنتج:</span>
                    <span id="selectedProduct">لم يتم الاختيار</span>
                </div>
                <div class="summary-row">
                    <span>السعر:</span>
                    <span id="selectedPrice">0 درهم</span>
                </div>
                <div class="summary-row">
                    <span>التوصيل:</span>
                    <span>مجاني</span>
                </div>
                <div class="summary-row summary-total">
                    <span>المجموع:</span>
                    <span id="totalPrice">0 درهم</span>
                </div>
            </div>

            <!-- Additional Notes -->
            <div class="form-group">
                <label for="notes">ملاحظات إضافية (اختياري)</label>
                <textarea id="notes" name="notes" rows="2" placeholder="أي ملاحظات أو طلبات خاصة..."></textarea>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="submit-btn">
                <i class="fas fa-shopping-cart"></i>
                تأكيد الطلب
            </button>

            <div class="security-info">
                <i class="fas fa-shield-alt"></i>
                معلوماتك محمية بالكامل | ضمان استرداد المال خلال 30 يوم
            </div>
        </form>
    </div>

    <script>
        // Product data
        const products = {
            'caffeine': {
                name: 'كافيين + فيتامين C',
                description: 'مكمل طبيعي للطاقة والتركيز',
                price: 149,
                originalPrice: 199
            },
            'omega3': {
                name: 'أوميغا 3 بلس',
                description: 'زيت السمك النقي لصحة القلب والدماغ',
                price: 179,
                originalPrice: 249
            },
            'multivitamin': {
                name: 'مالتي فيتامين كومبليت',
                description: 'تركيبة شاملة من 25 فيتامين ومعدن أساسي',
                price: 149,
                originalPrice: 199
            },
            'magnesium': {
                name: 'مغنيسيوم بلس',
                description: 'معدن أساسي للاسترخاء وصحة العضلات',
                price: 129,
                originalPrice: 169
            },
            'collagen': {
                name: 'كولاجين مارين',
                description: 'كولاجين بحري لصحة البشرة والمفاصل',
                price: 229,
                originalPrice: 299
            },
            'probiotics': {
                name: 'بروبيوتيك أدفانسد',
                description: '10 مليار بكتيريا نافعة لصحة الجهاز الهضمي',
                price: 169,
                originalPrice: 219
            },
            'zinc': {
                name: 'زنك بيسجليسينات',
                description: 'زنك عالي الامتصاص لتقوية المناعة',
                price: 99,
                originalPrice: 139
            }
        };

        // Bundle data
        const bundles = {
            'energy': {
                name: 'حزمة الطاقة والتركيز',
                description: 'كافيين + فيتامين C + مالتي فيتامين + مغنيسيوم',
                price: 349,
                originalPrice: 477
            },
            'complete': {
                name: 'حزمة الصحة الشاملة',
                description: 'كافيين + فيتامين C + أوميغا 3 + مالتي فيتامين + بروبيوتيك',
                price: 499,
                originalPrice: 676
            },
            'beauty': {
                name: 'حزمة الجمال والشباب',
                description: 'كولاجين مارين + فيتامين C + زنك',
                price: 349,
                originalPrice: 467
            }
        };

        // Initialize product based on URL parameters
        function initializeProduct() {
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('product');
            const bundleId = urlParams.get('bundle');

            let currentProduct;

            if (bundleId && bundles[bundleId]) {
                currentProduct = bundles[bundleId];
                document.querySelector('.form-header h1').textContent = 'طلب ' + currentProduct.name;
                document.querySelector('.form-header p').textContent = currentProduct.description;
            } else if (productId && products[productId]) {
                currentProduct = products[productId];
                document.querySelector('.form-header h1').textContent = 'طلب ' + currentProduct.name;
                document.querySelector('.form-header p').textContent = currentProduct.description;
            }

            if (currentProduct) {
                // Update package prices based on selected product
                updatePackagePrices(currentProduct.price);
            }
        }

        function updatePackagePrices(basePrice) {
            // Update single package
            const singlePackage = document.querySelector('[onclick*="single"]');
            if (singlePackage) {
                singlePackage.setAttribute('onclick', `selectPackage(this, 'single', ${basePrice})`);
                singlePackage.querySelector('.package-price').textContent = basePrice + ' درهم';
            }

            // Update double package (buy 1 get 1 free)
            const doublePackage = document.querySelector('[onclick*="double"]');
            if (doublePackage) {
                const doublePrice = basePrice; // Same price for 2 items
                doublePackage.setAttribute('onclick', `selectPackage(this, 'double', ${doublePrice})`);
                doublePackage.querySelector('.package-price').textContent = doublePrice + ' درهم';
            }

            // Update triple package (buy 2 get 1 free)
            const triplePackage = document.querySelector('[onclick*="triple"]');
            if (triplePackage) {
                const triplePrice = basePrice * 2; // Price for 2 items, get 1 free
                triplePackage.setAttribute('onclick', `selectPackage(this, 'triple', ${triplePrice})`);
                triplePackage.querySelector('.package-price').textContent = triplePrice + ' درهم';
            }
        }

        function selectPackage(element, packageType, price) {
            // Remove selected class from all packages
            document.querySelectorAll('.package-option').forEach(pkg => {
                pkg.classList.remove('selected');
            });
            
            // Add selected class to clicked package
            element.classList.add('selected');
            
            // Check the radio button
            element.querySelector('input[type="radio"]').checked = true;
            
            // Update order summary
            updateOrderSummary(packageType, price);
        }
        
        function updateOrderSummary(packageType, price) {
            const productNames = {
                'single': 'عبوة واحدة',
                'double': 'اشتري 1 + 1 مجاناً',
                'triple': 'اشتري 2 + 1 مجاناً'
            };
            
            document.getElementById('selectedProduct').textContent = productNames[packageType];
            document.getElementById('selectedPrice').textContent = price + ' درهم';
            document.getElementById('totalPrice').textContent = price + ' درهم';
        }
        
        function submitOrder(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const orderData = Object.fromEntries(formData);
            
            // Show loading state
            const submitBtn = event.target.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إرسال الطلب...';
            submitBtn.disabled = true;
            
            // Simulate order submission (replace with actual API call)
            setTimeout(() => {
                alert('تم إرسال طلبك بنجاح! سنتواصل معك خلال 24 ساعة.');
                console.log('Order Data:', orderData);
                
                // Reset form
                event.target.reset();
                document.querySelectorAll('.package-option').forEach(pkg => {
                    pkg.classList.remove('selected');
                });
                updateOrderSummary('', 0);
                
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                
                // Redirect to thank you page or main page
                // window.location.href = 'thank-you.html';
            }, 2000);
        }
        
        // Add form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('orderForm');
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() === '') {
                        this.style.borderColor = '#e74c3c';
                    } else {
                        this.style.borderColor = '#27ae60';
                    }
                });
            });
        });
    </script>
</body>
</html>
